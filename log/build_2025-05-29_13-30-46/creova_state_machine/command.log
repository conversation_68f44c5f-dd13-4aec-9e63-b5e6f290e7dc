Invoking command in '/home/<USER>/Dev/creova-state-machine': PYTHONPATH=/home/<USER>/Dev/creova-state-machine/build/creova_state_machine/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/creova_state_machine build --build-base /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build install --record /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/install.log --single-version-externally-managed install_data
Invoked command in '/home/<USER>/Dev/creova-state-machine' returned '0': PYTHONPATH=/home/<USER>/Dev/creova-state-machine/build/creova_state_machine/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/creova_state_machine build --build-base /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build install --record /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/install.log --single-version-externally-managed install_data
