[0.344s] Invoking command in '/home/<USER>/Dev/creova-state-machine': PYTHONPATH=/home/<USER>/Dev/creova-state-machine/build/creova_state_machine/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/creova_state_machine build --build-base /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build install --record /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/install.log --single-version-externally-managed install_data
[0.477s] running egg_info
[0.477s] creating build/creova_state_machine/creova_state_machine.egg-info
[0.477s] writing build/creova_state_machine/creova_state_machine.egg-info/PKG-INFO
[0.477s] writing dependency_links to build/creova_state_machine/creova_state_machine.egg-info/dependency_links.txt
[0.477s] writing entry points to build/creova_state_machine/creova_state_machine.egg-info/entry_points.txt
[0.477s] writing requirements to build/creova_state_machine/creova_state_machine.egg-info/requires.txt
[0.478s] writing top-level names to build/creova_state_machine/creova_state_machine.egg-info/top_level.txt
[0.478s] writing manifest file 'build/creova_state_machine/creova_state_machine.egg-info/SOURCES.txt'
[0.478s] reading manifest file 'build/creova_state_machine/creova_state_machine.egg-info/SOURCES.txt'
[0.479s] writing manifest file 'build/creova_state_machine/creova_state_machine.egg-info/SOURCES.txt'
[0.479s] running build
[0.479s] running build_py
[0.479s] creating /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build
[0.479s] creating /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib
[0.479s] creating /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine
[0.479s] copying creova_state_machine/__init__.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine
[0.479s] creating /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
[0.479s] copying creova_state_machine/nodes/destination_server_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
[0.479s] copying creova_state_machine/nodes/perception_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
[0.480s] copying creova_state_machine/nodes/__init__.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
[0.480s] copying creova_state_machine/nodes/navigation_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
[0.480s] copying creova_state_machine/nodes/orchestration_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
[0.480s] copying creova_state_machine/nodes/get_destination_client.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
[0.480s] copying creova_state_machine/nodes/hw_sw_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
[0.480s] copying creova_state_machine/nodes/manipulation_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
[0.480s] copying creova_state_machine/nodes/physical_ai_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
[0.480s] running install
[0.480s] running install_lib
[0.480s] creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine
[0.480s] copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/__init__.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine
[0.480s] creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
[0.480s] copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/destination_server_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
[0.481s] copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/perception_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
[0.481s] copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/__init__.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
[0.481s] copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/navigation_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
[0.481s] copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/orchestration_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
[0.481s] copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/get_destination_client.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
[0.481s] copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/hw_sw_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
[0.481s] copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/manipulation_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
[0.481s] copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/physical_ai_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
[0.481s] byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/__init__.py to __init__.cpython-310.pyc
[0.481s] byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/destination_server_node.py to destination_server_node.cpython-310.pyc
[0.482s] byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/perception_node.py to perception_node.cpython-310.pyc
[0.482s] byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/__init__.py to __init__.cpython-310.pyc
[0.482s] byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/navigation_node.py to navigation_node.cpython-310.pyc
[0.482s] byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/orchestration_node.py to orchestration_node.cpython-310.pyc
[0.483s] byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/get_destination_client.py to get_destination_client.cpython-310.pyc
[0.483s] byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/hw_sw_node.py to hw_sw_node.cpython-310.pyc
[0.483s] byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/manipulation_node.py to manipulation_node.cpython-310.pyc
[0.483s] byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/physical_ai_node.py to physical_ai_node.cpython-310.pyc
[0.484s] running install_data
[0.484s] creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/ament_index
[0.484s] creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/ament_index/resource_index
[0.484s] creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/ament_index/resource_index/packages
[0.484s] copying resource/creova_state_machine -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/ament_index/resource_index/packages
[0.484s] copying package.xml -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine
[0.484s] creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/launch
[0.484s] copying launch/orchestration.launch.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/launch
[0.484s] running install_egg_info
[0.485s] Copying build/creova_state_machine/creova_state_machine.egg-info to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine-0.0.1-py3.10.egg-info
[0.485s] running install_scripts
[0.499s] Installing destination_server_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
[0.499s] Installing get_destination_client script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
[0.499s] Installing hw_sw_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
[0.499s] Installing manipulation_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
[0.499s] Installing navigation_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
[0.499s] Installing navigation_tester script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
[0.499s] Installing orchestration_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
[0.499s] Installing perception_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
[0.499s] Installing physical_ai_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
[0.499s] writing list of installed files to '/home/<USER>/Dev/creova-state-machine/build/creova_state_machine/install.log'
[0.514s] Invoked command in '/home/<USER>/Dev/creova-state-machine' returned '0': PYTHONPATH=/home/<USER>/Dev/creova-state-machine/build/creova_state_machine/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/creova_state_machine build --build-base /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build install --record /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/install.log --single-version-externally-managed install_data
