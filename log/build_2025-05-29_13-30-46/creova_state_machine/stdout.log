running egg_info
creating build/creova_state_machine/creova_state_machine.egg-info
writing build/creova_state_machine/creova_state_machine.egg-info/PKG-INFO
writing dependency_links to build/creova_state_machine/creova_state_machine.egg-info/dependency_links.txt
writing entry points to build/creova_state_machine/creova_state_machine.egg-info/entry_points.txt
writing requirements to build/creova_state_machine/creova_state_machine.egg-info/requires.txt
writing top-level names to build/creova_state_machine/creova_state_machine.egg-info/top_level.txt
writing manifest file 'build/creova_state_machine/creova_state_machine.egg-info/SOURCES.txt'
reading manifest file 'build/creova_state_machine/creova_state_machine.egg-info/SOURCES.txt'
writing manifest file 'build/creova_state_machine/creova_state_machine.egg-info/SOURCES.txt'
running build
running build_py
creating /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build
creating /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib
creating /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine
copying creova_state_machine/__init__.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine
creating /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
copying creova_state_machine/nodes/destination_server_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
copying creova_state_machine/nodes/perception_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
copying creova_state_machine/nodes/__init__.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
copying creova_state_machine/nodes/navigation_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
copying creova_state_machine/nodes/orchestration_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
copying creova_state_machine/nodes/get_destination_client.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
copying creova_state_machine/nodes/hw_sw_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
copying creova_state_machine/nodes/manipulation_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
copying creova_state_machine/nodes/physical_ai_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes
running install
running install_lib
creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine
copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/__init__.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine
creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/destination_server_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/perception_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/__init__.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/navigation_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/orchestration_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/get_destination_client.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/hw_sw_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/manipulation_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/physical_ai_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes
byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/__init__.py to __init__.cpython-310.pyc
byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/destination_server_node.py to destination_server_node.cpython-310.pyc
byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/perception_node.py to perception_node.cpython-310.pyc
byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/__init__.py to __init__.cpython-310.pyc
byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/navigation_node.py to navigation_node.cpython-310.pyc
byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/orchestration_node.py to orchestration_node.cpython-310.pyc
byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/get_destination_client.py to get_destination_client.cpython-310.pyc
byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/hw_sw_node.py to hw_sw_node.cpython-310.pyc
byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/manipulation_node.py to manipulation_node.cpython-310.pyc
byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/physical_ai_node.py to physical_ai_node.cpython-310.pyc
running install_data
creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/ament_index
creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/ament_index/resource_index
creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/ament_index/resource_index/packages
copying resource/creova_state_machine -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/ament_index/resource_index/packages
copying package.xml -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine
creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/launch
copying launch/orchestration.launch.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/launch
running install_egg_info
Copying build/creova_state_machine/creova_state_machine.egg-info to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine-0.0.1-py3.10.egg-info
running install_scripts
Installing destination_server_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
Installing get_destination_client script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
Installing hw_sw_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
Installing manipulation_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
Installing navigation_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
Installing navigation_tester script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
Installing orchestration_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
Installing perception_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
Installing physical_ai_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine
writing list of installed files to '/home/<USER>/Dev/creova-state-machine/build/creova_state_machine/install.log'
