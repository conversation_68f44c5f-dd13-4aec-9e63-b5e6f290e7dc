[0.000000] (-) TimerEvent: {}
[0.000814] (creova_state_machine) JobQueued: {'identifier': 'creova_state_machine', 'dependencies': OrderedDict()}
[0.000865] (creova_state_machine) JobStarted: {'identifier': 'creova_state_machine'}
[0.099729] (-) TimerEvent: {}
[0.200081] (-) TimerEvent: {}
[0.300420] (-) TimerEvent: {}
[0.343139] (creova_state_machine) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', 'build/creova_state_machine', 'build', '--build-base', '/home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build', 'install', '--record', '/home/<USER>/Dev/creova-state-machine/build/creova_state_machine/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/Dev/creova-state-machine', 'env': {'GJS_DEBUG_TOPICS': 'JS ERROR;JS LOG', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'matthew', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib', 'LESS': '-FX', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'OLDPWD': '/home/<USER>/Dev/creova-state-machine', 'TERM_PROGRAM_VERSION': '1.100.2', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '2218', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'SYSTEMD_EXEC_PID': '2376', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy', 'IM_CONFIG_CHECK_ENV': '1', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '10744', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'ROS_DISTRO': 'humble', 'LOGNAME': 'matthew', 'JOURNAL_STREAM': '8:10379', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'matthew', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'ROS_LOCALHOST_ONLY': '0', 'PATH': '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand', 'SESSION_MANAGER': 'local/matthew-Legion-5-16IRX9:@/tmp/.ICE-unix/2341,unix/matthew-Legion-5-16IRX9:/tmp/.ICE-unix/2341', 'INVOCATION_ID': '00397cd489c64484bd4a37b158e0d6e0', 'XDG_MENU_PREFIX': 'gnome-', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-164d5fe3f236b2d2.txt', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.VP2262', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-420a2934f7.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AGENT_LAUNCHER': 'gnome-keyring', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/opt/ros/humble', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'GJS_DEBUG_OUTPUT': 'stderr', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/Dev/creova-state-machine/build/creova_state_machine', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/Dev/creova-state-machine/build/creova_state_machine/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages', 'COLCON': '1'}, 'shell': False}
[0.400542] (-) TimerEvent: {}
[0.477550] (creova_state_machine) StdoutLine: {'line': b'running egg_info\n'}
[0.478033] (creova_state_machine) StdoutLine: {'line': b'creating build/creova_state_machine/creova_state_machine.egg-info\n'}
[0.478079] (creova_state_machine) StdoutLine: {'line': b'writing build/creova_state_machine/creova_state_machine.egg-info/PKG-INFO\n'}
[0.478205] (creova_state_machine) StdoutLine: {'line': b'writing dependency_links to build/creova_state_machine/creova_state_machine.egg-info/dependency_links.txt\n'}
[0.478330] (creova_state_machine) StdoutLine: {'line': b'writing entry points to build/creova_state_machine/creova_state_machine.egg-info/entry_points.txt\n'}
[0.478376] (creova_state_machine) StdoutLine: {'line': b'writing requirements to build/creova_state_machine/creova_state_machine.egg-info/requires.txt\n'}
[0.478409] (creova_state_machine) StdoutLine: {'line': b'writing top-level names to build/creova_state_machine/creova_state_machine.egg-info/top_level.txt\n'}
[0.478519] (creova_state_machine) StdoutLine: {'line': b"writing manifest file 'build/creova_state_machine/creova_state_machine.egg-info/SOURCES.txt'\n"}
[0.479295] (creova_state_machine) StdoutLine: {'line': b"reading manifest file 'build/creova_state_machine/creova_state_machine.egg-info/SOURCES.txt'\n"}
[0.479771] (creova_state_machine) StdoutLine: {'line': b"writing manifest file 'build/creova_state_machine/creova_state_machine.egg-info/SOURCES.txt'\n"}
[0.479919] (creova_state_machine) StdoutLine: {'line': b'running build\n'}
[0.479956] (creova_state_machine) StdoutLine: {'line': b'running build_py\n'}
[0.480017] (creova_state_machine) StdoutLine: {'line': b'creating /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build\n'}
[0.480050] (creova_state_machine) StdoutLine: {'line': b'creating /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib\n'}
[0.480081] (creova_state_machine) StdoutLine: {'line': b'creating /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine\n'}
[0.480209] (creova_state_machine) StdoutLine: {'line': b'copying creova_state_machine/__init__.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine\n'}
[0.480293] (creova_state_machine) StdoutLine: {'line': b'creating /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes\n'}
[0.480330] (creova_state_machine) StdoutLine: {'line': b'copying creova_state_machine/nodes/destination_server_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes\n'}
[0.480365] (creova_state_machine) StdoutLine: {'line': b'copying creova_state_machine/nodes/perception_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes\n'}
[0.480399] (creova_state_machine) StdoutLine: {'line': b'copying creova_state_machine/nodes/__init__.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes\n'}
[0.480434] (creova_state_machine) StdoutLine: {'line': b'copying creova_state_machine/nodes/navigation_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes\n'}
[0.480465] (creova_state_machine) StdoutLine: {'line': b'copying creova_state_machine/nodes/orchestration_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes\n'}
[0.480523] (creova_state_machine) StdoutLine: {'line': b'copying creova_state_machine/nodes/get_destination_client.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes\n'}
[0.480594] (creova_state_machine) StdoutLine: {'line': b'copying creova_state_machine/nodes/hw_sw_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes\n'}
[0.480628] (creova_state_machine) StdoutLine: {'line': b'copying creova_state_machine/nodes/manipulation_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes\n'}
[0.480658] (creova_state_machine) StdoutLine: {'line': b'copying creova_state_machine/nodes/physical_ai_node.py -> /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes\n'}
[0.480689] (creova_state_machine) StdoutLine: {'line': b'running install\n'}
[0.480766] (creova_state_machine) StdoutLine: {'line': b'running install_lib\n'}
[0.481179] (creova_state_machine) StdoutLine: {'line': b'creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine\n'}
[0.481267] (creova_state_machine) StdoutLine: {'line': b'copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/__init__.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine\n'}
[0.481329] (creova_state_machine) StdoutLine: {'line': b'creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes\n'}
[0.481362] (creova_state_machine) StdoutLine: {'line': b'copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/destination_server_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes\n'}
[0.481419] (creova_state_machine) StdoutLine: {'line': b'copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/perception_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes\n'}
[0.481475] (creova_state_machine) StdoutLine: {'line': b'copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/__init__.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes\n'}
[0.481532] (creova_state_machine) StdoutLine: {'line': b'copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/navigation_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes\n'}
[0.481564] (creova_state_machine) StdoutLine: {'line': b'copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/orchestration_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes\n'}
[0.481596] (creova_state_machine) StdoutLine: {'line': b'copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/get_destination_client.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes\n'}
[0.481659] (creova_state_machine) StdoutLine: {'line': b'copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/hw_sw_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes\n'}
[0.481692] (creova_state_machine) StdoutLine: {'line': b'copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/manipulation_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes\n'}
[0.481720] (creova_state_machine) StdoutLine: {'line': b'copying /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build/lib/creova_state_machine/nodes/physical_ai_node.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes\n'}
[0.481864] (creova_state_machine) StdoutLine: {'line': b'byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/__init__.py to __init__.cpython-310.pyc\n'}
[0.481976] (creova_state_machine) StdoutLine: {'line': b'byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/destination_server_node.py to destination_server_node.cpython-310.pyc\n'}
[0.482476] (creova_state_machine) StdoutLine: {'line': b'byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/perception_node.py to perception_node.cpython-310.pyc\n'}
[0.482704] (creova_state_machine) StdoutLine: {'line': b'byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/__init__.py to __init__.cpython-310.pyc\n'}
[0.482758] (creova_state_machine) StdoutLine: {'line': b'byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/navigation_node.py to navigation_node.cpython-310.pyc\n'}
[0.483250] (creova_state_machine) StdoutLine: {'line': b'byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/orchestration_node.py to orchestration_node.cpython-310.pyc\n'}
[0.483821] (creova_state_machine) StdoutLine: {'line': b'byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/get_destination_client.py to get_destination_client.cpython-310.pyc\n'}
[0.483966] (creova_state_machine) StdoutLine: {'line': b'byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/hw_sw_node.py to hw_sw_node.cpython-310.pyc\n'}
[0.484127] (creova_state_machine) StdoutLine: {'line': b'byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/manipulation_node.py to manipulation_node.cpython-310.pyc\n'}
[0.484349] (creova_state_machine) StdoutLine: {'line': b'byte-compiling /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine/nodes/physical_ai_node.py to physical_ai_node.cpython-310.pyc\n'}
[0.484648] (creova_state_machine) StdoutLine: {'line': b'running install_data\n'}
[0.484717] (creova_state_machine) StdoutLine: {'line': b'creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/ament_index\n'}
[0.484768] (creova_state_machine) StdoutLine: {'line': b'creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/ament_index/resource_index\n'}
[0.484830] (creova_state_machine) StdoutLine: {'line': b'creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/ament_index/resource_index/packages\n'}
[0.484861] (creova_state_machine) StdoutLine: {'line': b'copying resource/creova_state_machine -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/ament_index/resource_index/packages\n'}
[0.484892] (creova_state_machine) StdoutLine: {'line': b'copying package.xml -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine\n'}
[0.484949] (creova_state_machine) StdoutLine: {'line': b'creating /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/launch\n'}
[0.485003] (creova_state_machine) StdoutLine: {'line': b'copying launch/orchestration.launch.py -> /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/launch\n'}
[0.485034] (creova_state_machine) StdoutLine: {'line': b'running install_egg_info\n'}
[0.486015] (creova_state_machine) StdoutLine: {'line': b'Copying build/creova_state_machine/creova_state_machine.egg-info to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages/creova_state_machine-0.0.1-py3.10.egg-info\n'}
[0.486329] (creova_state_machine) StdoutLine: {'line': b'running install_scripts\n'}
[0.499601] (creova_state_machine) StdoutLine: {'line': b'Installing destination_server_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine\n'}
[0.499676] (creova_state_machine) StdoutLine: {'line': b'Installing get_destination_client script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine\n'}
[0.499761] (creova_state_machine) StdoutLine: {'line': b'Installing hw_sw_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine\n'}
[0.499793] (creova_state_machine) StdoutLine: {'line': b'Installing manipulation_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine\n'}
[0.499822] (creova_state_machine) StdoutLine: {'line': b'Installing navigation_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine\n'}
[0.499852] (creova_state_machine) StdoutLine: {'line': b'Installing navigation_tester script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine\n'}
[0.499880] (creova_state_machine) StdoutLine: {'line': b'Installing orchestration_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine\n'}
[0.499909] (creova_state_machine) StdoutLine: {'line': b'Installing perception_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine\n'}
[0.499936] (creova_state_machine) StdoutLine: {'line': b'Installing physical_ai_node script to /home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/creova_state_machine\n'}
[0.500021] (creova_state_machine) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/Dev/creova-state-machine/build/creova_state_machine/install.log'\n"}
[0.500620] (-) TimerEvent: {}
[0.514761] (creova_state_machine) CommandEnded: {'returncode': 0}
[0.520411] (creova_state_machine) JobEnded: {'identifier': 'creova_state_machine', 'rc': 0}
[0.520876] (-) EventReactorShutdown: {}
