[0.065s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.065s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=32, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x761542a2f610>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x761542b68a30>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x761542b68a30>>)
[0.165s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.165s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.165s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.165s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.165s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.166s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.166s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/Dev/creova-state-machine'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.175s] DEBUG:colcon.colcon_core.package_identification:Package '.' with type 'ros.ament_python' and name 'creova_state_machine'
[0.175s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.175s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.175s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.175s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.175s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.189s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.189s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.190s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 310 installed packages in /opt/ros/humble
[0.196s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.218s] Level 5:colcon.colcon_core.verb:set package 'creova_state_machine' build argument 'cmake_args' from command line to 'None'
[0.218s] Level 5:colcon.colcon_core.verb:set package 'creova_state_machine' build argument 'cmake_target' from command line to 'None'
[0.218s] Level 5:colcon.colcon_core.verb:set package 'creova_state_machine' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.218s] Level 5:colcon.colcon_core.verb:set package 'creova_state_machine' build argument 'cmake_clean_cache' from command line to 'False'
[0.218s] Level 5:colcon.colcon_core.verb:set package 'creova_state_machine' build argument 'cmake_clean_first' from command line to 'False'
[0.218s] Level 5:colcon.colcon_core.verb:set package 'creova_state_machine' build argument 'cmake_force_configure' from command line to 'False'
[0.218s] Level 5:colcon.colcon_core.verb:set package 'creova_state_machine' build argument 'ament_cmake_args' from command line to 'None'
[0.218s] Level 5:colcon.colcon_core.verb:set package 'creova_state_machine' build argument 'catkin_cmake_args' from command line to 'None'
[0.218s] Level 5:colcon.colcon_core.verb:set package 'creova_state_machine' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.218s] DEBUG:colcon.colcon_core.verb:Building package 'creova_state_machine' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Dev/creova-state-machine/build/creova_state_machine', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine', 'merge_install': False, 'path': '/home/<USER>/Dev/creova-state-machine', 'symlink_install': False, 'test_result_base': None}
[0.218s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.219s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.219s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/Dev/creova-state-machine' with build type 'ament_python'
[0.220s] Level 1:colcon.colcon_core.shell:create_environment_hook('creova_state_machine', 'ament_prefix_path')
[0.221s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.221s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/hook/ament_prefix_path.ps1'
[0.221s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/hook/ament_prefix_path.dsv'
[0.222s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/hook/ament_prefix_path.sh'
[0.222s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.222s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.367s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/Dev/creova-state-machine'
[0.367s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.368s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.565s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Dev/creova-state-machine': PYTHONPATH=/home/<USER>/Dev/creova-state-machine/build/creova_state_machine/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/creova_state_machine build --build-base /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build install --record /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/install.log --single-version-externally-managed install_data
[0.734s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Dev/creova-state-machine' returned '0': PYTHONPATH=/home/<USER>/Dev/creova-state-machine/build/creova_state_machine/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/creova_state_machine build --build-base /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/build install --record /home/<USER>/Dev/creova-state-machine/build/creova_state_machine/install.log --single-version-externally-managed install_data
[0.736s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine' for CMake module files
[0.736s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine' for CMake config files
[0.736s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib'
[0.736s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/bin'
[0.736s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/pkgconfig/creova_state_machine.pc'
[0.736s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/lib/python3.10/site-packages'
[0.737s] Level 1:colcon.colcon_core.shell:create_environment_hook('creova_state_machine', 'pythonpath')
[0.737s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/hook/pythonpath.ps1'
[0.737s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/hook/pythonpath.dsv'
[0.737s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/hook/pythonpath.sh'
[0.737s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/bin'
[0.737s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(creova_state_machine)
[0.738s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/package.ps1'
[0.738s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/package.dsv'
[0.738s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/package.sh'
[0.739s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/package.bash'
[0.739s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/creova_state_machine/package.zsh'
[0.739s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Dev/creova-state-machine/install/creova_state_machine/share/colcon-core/packages/creova_state_machine)
[0.740s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.740s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.740s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.740s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.743s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.743s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.743s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.762s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.762s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Dev/creova-state-machine/install/local_setup.ps1'
[0.764s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Dev/creova-state-machine/install/_local_setup_util_ps1.py'
[0.766s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Dev/creova-state-machine/install/setup.ps1'
[0.767s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Dev/creova-state-machine/install/local_setup.sh'
[0.768s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Dev/creova-state-machine/install/_local_setup_util_sh.py'
[0.768s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Dev/creova-state-machine/install/setup.sh'
[0.769s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Dev/creova-state-machine/install/local_setup.bash'
[0.769s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Dev/creova-state-machine/install/setup.bash'
[0.770s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Dev/creova-state-machine/install/local_setup.zsh'
[0.770s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Dev/creova-state-machine/install/setup.zsh'
