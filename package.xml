<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>creova_state_machine</name>
  <version>0.0.1</version>
  <description>Voice-controlled delivery robot system with navigation and perception capabilities</description>
  <maintainer email="<EMAIL>">gixstudent</maintainer>
  <license>Apache License 2.0</license>

  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>

  <buildtool_depend>ament_python</buildtool_depend>
  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>rclpy</depend>
  <depend>std_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>tf2_ros</depend>
  <depend>creova_state_machine_interfaces</depend>
  <depend>custom_msgs</depend>
  <exec_depend>std_srvs</exec_depend>

  <export>
    <build_type>ament_python</build_type>
  </export>
</package>